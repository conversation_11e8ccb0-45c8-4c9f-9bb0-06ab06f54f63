package by.algin.projectservice.controller;

import by.algin.dto.project.*;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.PagedResponse;
import by.algin.constants.CommonPathConstants;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.util.PaginationHelper;
import by.algin.projectservice.service.ProjectService;
import by.algin.projectservice.util.ValidationUtils;
import by.algin.util.SecurityUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(CommonPathConstants.API_PROJECTS_PATH)
@RequiredArgsConstructor
@Slf4j
public class ProjectController {

    private final ProjectService projectService;
    private final ValidationUtils validationUtils;
    private final PaginationHelper paginationHelper;

    @PostMapping
    public ResponseEntity<ProjectResponse> createProject(@Valid @RequestBody CreateProjectRequest request) {
        log.info("Creating project: {}", request.getName());

        validationUtils.validateProjectName(request.getName());
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} creating project: {}", currentUserId, request.getName());

        ProjectResponse response = projectService.createProject(request, currentUserId);

        log.info("Project created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping(CommonPathConstants.PATH_VAR_PROJECT_ID)
    public ResponseEntity<ProjectResponse> getProject(@PathVariable Long projectId) {
        log.debug("Getting project: {}", projectId);

        validationUtils.validateProjectId(projectId);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} getting project: {}", currentUserId, projectId);

        ProjectResponse response = projectService.getProjectById(projectId, currentUserId);
        return ResponseEntity.ok(response);
    }

    @PutMapping(CommonPathConstants.PATH_VAR_PROJECT_ID)
    public ResponseEntity<ProjectResponse> updateProject(@PathVariable Long projectId,
                                                        @Valid @RequestBody UpdateProjectRequest request) {
        log.info("Updating project: {}", projectId);

        validationUtils.validateProjectId(projectId);
        if (request.getName() != null) {
            validationUtils.validateProjectName(request.getName());
        }

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} updating project: {}", currentUserId, projectId);

        ProjectResponse response = projectService.updateProject(projectId, request, currentUserId);

        log.info("Project {} updated successfully", projectId);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping(CommonPathConstants.PATH_VAR_PROJECT_ID)
    public ResponseEntity<ApiResponse<String>> deleteProject(@PathVariable Long projectId) {
        log.info("Deleting project: {}", projectId);

        validationUtils.validateProjectId(projectId);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} deleting project: {}", currentUserId, projectId);

        projectService.deleteProject(projectId, currentUserId);

        log.info("Project {} deleted successfully", projectId);
        return ResponseEntity.ok(ApiResponse.success(ProjectMessageConstants.PROJECT_DELETED_SUCCESSFULLY));
    }

    @GetMapping(CommonPathConstants.API_PROJECTS_BY_USER)
    public ResponseEntity<PagedResponse<ProjectResponse>> getCurrentUserProjects(
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDir) {

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} getting own projects with pagination: page={}, size={}", currentUserId, page, size);

        PagedResponse<ProjectResponse> response = projectService.getUserProjects(
                currentUserId, paginationHelper.createPageable(page, size, sortBy, sortDir));
        return ResponseEntity.ok(response);
    }

    @GetMapping(CommonPathConstants.API_PROJECTS_BY_USER_ID)
    public ResponseEntity<PagedResponse<ProjectResponse>> getUserProjects(
            @PathVariable Long userId,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDir) {

        validationUtils.validateUserId(userId);

        log.debug("Getting projects for user {} with pagination: page={}, size={}", userId, page, size);

        PagedResponse<ProjectResponse> response = projectService.getUserProjects(
                userId, paginationHelper.createPageable(page, size, sortBy, sortDir));
        return ResponseEntity.ok(response);
    }

    @PostMapping(CommonPathConstants.PATH_VAR_PROJECT_ID_MEMBERS)
    public ResponseEntity<ProjectMemberResponse> addProjectMember(@PathVariable Long projectId,
                                                                 @Valid @RequestBody AddProjectMemberRequest request) {
        log.info("Adding member to project: {}", projectId);

        validationUtils.validateProjectId(projectId);
        validationUtils.validateUserId(request.getUserId());

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} adding member {} to project {}", currentUserId, request.getUserId(), projectId);

        ProjectMemberResponse response = projectService.addProjectMember(projectId, request, currentUserId);

        log.info("Member {} added to project {} successfully", request.getUserId(), projectId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping(CommonPathConstants.PATH_VAR_PROJECT_ID_USER_ID_ROLE)
    public ResponseEntity<ProjectMemberResponse> updateMemberRole(@PathVariable Long projectId,
                                                                 @PathVariable Long userId,
                                                                 @Valid @RequestBody UpdateProjectMemberRoleRequest request) {
        log.info("Updating member role in project: {} for user: {}", projectId, userId);

        validationUtils.validateProjectId(projectId);
        validationUtils.validateUserId(userId);

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} updating role for member {} in project {}", currentUserId, userId, projectId);

        ProjectMemberResponse response = projectService.updateMemberRole(projectId, userId, request, currentUserId);

        log.info("Member {} role updated in project {} successfully", userId, projectId);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping(CommonPathConstants.PATH_VAR_PROJECT_ID_USER_ID)
    public ResponseEntity<ApiResponse<String>> removeProjectMember(@PathVariable Long projectId, @PathVariable Long userId) {
        log.info("Removing member from project: {} user: {}", projectId, userId);

        validationUtils.validateProjectId(projectId);
        validationUtils.validateUserId(userId);

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} removing member {} from project {}", currentUserId, userId, projectId);

        projectService.removeProjectMember(projectId, userId, currentUserId);

        log.info("Member {} removed from project {} successfully", userId, projectId);
        return ResponseEntity.ok(ApiResponse.success(ProjectMessageConstants.MEMBER_REMOVED_SUCCESSFULLY));
    }

    @GetMapping(CommonPathConstants.PATH_VAR_PROJECT_ID_MEMBERS)
    public ResponseEntity<PagedResponse<ProjectMemberResponse>> getProjectMembers(
            @PathVariable Long projectId,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDir) {

        validationUtils.validateProjectId(projectId);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();

        log.debug("User {} getting members for project {} with pagination: page={}, size={}",
                currentUserId, projectId, page, size);

        PagedResponse<ProjectMemberResponse> members = projectService.getProjectMembers(
                projectId, currentUserId, paginationHelper.createMembersPageable(page, size, sortBy, sortDir));
        return ResponseEntity.ok(members);
    }

    @GetMapping(CommonPathConstants.TEST_ENDPOINT)
    public ResponseEntity<String> test() {
        log.debug("Test endpoint called (simplified version)");
        return ResponseEntity.ok("Simplified " + ProjectMessageConstants.TEST_SERVICE_WORKING);
    }

    @GetMapping(CommonPathConstants.ROLE_ENDPOINT)
    public ResponseEntity<ApiResponse<by.algin.projectservice.enums.ProjectRole[]>> getAvailableRoles() {
        log.debug("Getting available project roles");
        return ResponseEntity.ok(ApiResponse.success(by.algin.projectservice.enums.ProjectRole.values()));
    }

    @GetMapping(CommonPathConstants.ROLE_ENDPOINT + "/{roleName}/permissions")
    public ResponseEntity<ApiResponse<by.algin.projectservice.enums.Permission[]>> getRolePermissions(@PathVariable String roleName) {
        log.debug("Getting permissions for role: {}", roleName);

        try {
            by.algin.projectservice.enums.ProjectRole role = by.algin.projectservice.enums.ProjectRole.valueOf(roleName.toUpperCase());
            by.algin.projectservice.enums.Permission[] permissions = role.getPermissions().toArray(new by.algin.projectservice.enums.Permission[0]);
            return ResponseEntity.ok(ApiResponse.success(permissions));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("INVALID_ROLE", "Invalid role name: " + roleName));
        }
    }
}

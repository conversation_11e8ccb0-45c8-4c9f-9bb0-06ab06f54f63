package by.algin.projectservice.service;

import by.algin.projectservice.enums.Permission;
import by.algin.projectservice.enums.ProjectRole;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.exception.ProjectAccessDeniedException;
import by.algin.projectservice.repository.ProjectMemberRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectSecurityService {

    private final ProjectMemberRepository projectMemberRepository;

    @Cacheable(value = "user-permissions", key = "#projectId + '_' + #userId + '_' + #permission")
    public boolean hasPermission(Long projectId, Long userId, Permission permission) {
        log.debug("Checking permission '{}' for user {} on project {}", permission, userId, projectId);

        Optional<ProjectMember> memberOpt = projectMemberRepository.findByProjectIdAndUserId(projectId, userId);
        if (memberOpt.isEmpty()) {
            log.debug("User {} is not a member of project {}", userId, projectId);
            return false;
        }

        ProjectMember member = memberOpt.get();
        ProjectRole role = member.getRole();
        
        if (role == null) {
            log.warn("Unknown role '{}' for user {} in project {}", member.getRole(), userId, projectId);
            return false;
        }

        boolean hasPermission = role.hasPermission(permission);
        log.debug("User {} with role '{}' has permission '{}' on project {}: {}", 
                userId, role, permission, projectId, hasPermission);

        return hasPermission;
    }

    public boolean canViewProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, Permission.VIEW);
    }

    public boolean canEditProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, Permission.EDIT);
    }

    public boolean canDeleteProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, Permission.DELETE);
    }

    public boolean canManageMembers(Long projectId, Long userId) {
        return hasPermission(projectId, userId, Permission.MANAGE_MEMBERS);
    }

    public boolean canInviteMembers(Long projectId, Long userId) {
        return hasPermission(projectId, userId, Permission.INVITE);
    }

    public void requirePermission(Long projectId, Long userId, Permission permission) {
        if (!hasPermission(projectId, userId, permission)) {
            log.warn("Access denied: User {} lacks permission '{}' for project {}", userId, permission, projectId);
            throw new ProjectAccessDeniedException(userId, projectId);
        }
    }

    public void requireViewAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, Permission.VIEW);
    }

    public void requireEditAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, Permission.EDIT);
    }

    public void requireDeleteAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, Permission.DELETE);
    }

    public void requireMemberManagementAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, Permission.MANAGE_MEMBERS);
    }

    public void requireInviteAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, Permission.INVITE);
    }

    @Cacheable(value = "user-roles", key = "#projectId + '_' + #userId")
    public Optional<ProjectRole> getUserRole(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .map(member -> member.getRole());
    }

    @Cacheable(value = "user-all-permissions", key = "#projectId + '_' + #userId")
    public Set<Permission> getUserPermissions(Long projectId, Long userId) {
        return getUserRole(projectId, userId)
                .map(ProjectRole::getPermissions)
                .orElse(Set.of());
    }

    @Cacheable(value = "project-membership", key = "#projectId + '_' + #userId")
    public boolean isMember(Long projectId, Long userId) {
        return projectMemberRepository.existsByProjectIdAndUserId(projectId, userId);
    }

    public boolean canAssignRole(Long projectId, Long userId, ProjectRole targetRole) {
        Optional<ProjectRole> userRole = getUserRole(projectId, userId);
        return userRole.isPresent() && userRole.get().canAssignRole(targetRole);
    }

    public void requireRoleAssignmentAccess(Long projectId, Long userId, ProjectRole targetRole) {
        if (!canAssignRole(projectId, userId, targetRole)) {
            log.warn("Access denied: User {} cannot assign role '{}' in project {}", userId, targetRole, projectId);
            throw new ProjectAccessDeniedException(userId, projectId);
        }
    }

    public ProjectMember getMemberOrThrow(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> {
                    log.warn("User {} is not a member of project {}", userId, projectId);
                    return new ProjectAccessDeniedException(userId, projectId);
                });
    }

}

package by.algin.projectservice.constants;

/**
 * Local API constants for ProjectService
 * Used when CommonDTOs constants are not available
 */
public final class ApiConstants {

    // User API paths
    public static final String API_USERS_BY_ID = "/users/{userId}";
    public static final String API_USERS = "/api/users";
    public static final String API_USERS_SEARCH = "/users/search";
    public static final String API_USERS_EXISTS = "/users/{userId}/exists";
    public static final String API_AUTH_VALIDATE = "/auth/validate";

    // Path parameters
    public static final String PARAM_USER_ID = "userId";
    public static final String PARAM_FIELD = "field";
    public static final String PARAM_VALUE = "value";

    // Project API paths
    public static final String API_PATH = "/api";
    public static final String PATH_VAR_PROJECT_ID = "/{projectId}";
    public static final String PATH_VAR_PROJECT_ID_MEMBERS = "/{projectId}/members";
    public static final String PATH_VAR_PROJECT_ID_USER_ID_ROLE = "/{projectId}/members/{userId}/role";
    public static final String PATH_VAR_PROJECT_ID_USER_ID = "/{projectId}/members/{userId}";

    // Common endpoints
    public static final String TEST_ENDPOINT = "/test";
    public static final String ROLES_ENDPOINT = "/roles";

    private ApiConstants() {
        // Utility class
    }
}

package by.algin.projectservice.service;

import by.algin.dto.project.AddProjectMemberRequest;
import by.algin.dto.project.ProjectMemberResponse;
import by.algin.dto.project.UpdateProjectMemberRoleRequest;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.enums.ProjectRole;
import by.algin.projectservice.exception.ProjectMemberNotFoundException;
import by.algin.projectservice.exception.ProjectNotFoundException;
import by.algin.projectservice.repository.ProjectMemberRepository;
import by.algin.projectservice.repository.ProjectRepository;
import by.algin.projectservice.util.PaginationHelper;
import by.algin.projectservice.util.UserBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectMemberService {

    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final ProjectSecurityService securityService;
    private final UserBaseService userBaseService;

    @Transactional
    public ProjectMemberResponse addProjectMember(Long projectId, AddProjectMemberRequest request, Long currentUserId) {
        log.debug("Adding user {} to project {} with role {} by user {}",
                request.getUserId(), projectId, request.getRole(), currentUserId);

        Project project = getProjectOrThrow(projectId);
        securityService.requireMemberManagementAccess(projectId, currentUserId);

        Long userId = request.getUserId();
        by.algin.dto.project.ProjectRole requestRoleDto = request.getRole() != null ?
            request.getRole() : by.algin.dto.project.ProjectRole.DEVELOPER;
        ProjectRole role = ProjectRole.valueOf(requestRoleDto.name());

        // Проверяем, что пользователь еще не участник проекта
        if (projectMemberRepository.existsByProjectIdAndUserId(projectId, userId)) {
            throw new IllegalArgumentException("User is already a member of this project");
        }

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);

        ProjectMember member = createProjectMember(project, userId, role);
        member = projectMemberRepository.save(member);

        log.debug("Successfully added user {} to project {}", request.getUserId(), projectId);

        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public ProjectMemberResponse updateProjectMemberRole(Long projectId, Long userId,
                                                        UpdateProjectMemberRoleRequest request, Long currentUserId) {
        log.debug("Updating role of user {} in project {} to {} by user {}",
                userId, projectId, request.getRole(), currentUserId);

        securityService.requireMemberManagementAccess(projectId, currentUserId);

        ProjectMember member = getProjectMemberOrThrow(projectId, userId);
        by.algin.dto.project.ProjectRole newRoleDto = request.getRole();
        ProjectRole newRole = ProjectRole.valueOf(newRoleDto.name());

        // Проверяем права на изменение роли
        if (member.getRole() == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot change role of project owner");
        }

        if (newRole == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot promote member to owner role");
        }

        member.setRole(newRole);
        member = projectMemberRepository.save(member);

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);
        log.info("Successfully updated role of user {} in project {}", userId, projectId);
        
        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public void removeProjectMember(Long projectId, Long userId, Long currentUserId) {
        log.info("Removing user {} from project {} by user {}", userId, projectId, currentUserId);

        securityService.requireMemberManagementAccess(projectId, currentUserId);

        ProjectMember member = getProjectMemberOrThrow(projectId, userId);

        // Нельзя удалить владельца проекта
        if (member.getRole() == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot remove project owner");
        }

        projectMemberRepository.delete(member);

        log.info("Successfully removed user {} from project {}", userId, projectId);
    }

    @Transactional(readOnly = true)
    public List<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId) {
        log.debug("Getting members of project {} for user {}", projectId, currentUserId);

        Project project = getProjectOrThrow(projectId);
        validateProjectAccess(project, currentUserId);

        List<ProjectMember> members = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId);
        return convertMembersToResponses(members);
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId, Pageable pageable) {
        log.info("Getting members for project {} with pagination: page={}, size={}",
                projectId, pageable.getPageNumber(), pageable.getPageSize());

        Project project = getProjectOrThrow(projectId);
        validateProjectAccess(project, currentUserId);

        Page<ProjectMember> membersPage = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId, pageable);
        List<ProjectMember> members = membersPage.getContent();
        Map<Long, UserResponse> users = getUsersForMembers(members);

        Page<ProjectMemberResponse> responsePage = membersPage.map(member -> {
            UserResponse user = users.get(member.getUserId());
            if (user == null) {
                log.warn("User not found for member {}, user ID: {}", member.getId(), member.getUserId());
            }
            return convertToProjectMemberResponse(member, user);
        });

        return PaginationHelper.toPagedResponse(responsePage);
    }

    private Project getProjectOrThrow(Long projectId) {
        return projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));
    }

    private ProjectMember getProjectMemberOrThrow(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));
    }

    private void validateProjectAccess(Project project, Long userId) {
        if (!project.getOwnerId().equals(userId) &&
            !projectMemberRepository.existsByProjectIdAndUserId(project.getId(), userId)) {
            throw new by.algin.projectservice.exception.ProjectAccessDeniedException(userId, project.getId());
        }
    }



    private ProjectMember createProjectMember(Project project, Long userId, ProjectRole role) {
        return ProjectMember.builder()
                .project(project)
                .userId(userId)
                .role(role)
                .build();
    }

    private List<ProjectMemberResponse> convertMembersToResponses(List<ProjectMember> members) {
        Map<Long, UserResponse> users = getUsersForMembers(members);
        return members.stream()
                .map(member -> convertToProjectMemberResponse(member, users.get(member.getUserId())))
                .collect(Collectors.toList());
    }

    private Map<Long, UserResponse> getUsersForMembers(List<ProjectMember> members) {
        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());
        return userBaseService.getUsersByIdsWithFallback(userIds);
    }

    private ProjectMemberResponse convertToProjectMemberResponse(ProjectMember member, UserResponse user) {
        if (user == null) {
            throw new IllegalStateException("User data not found for user ID: " + member.getUserId());
        }

        by.algin.dto.project.ProjectRole roleDto =
            by.algin.dto.project.ProjectRole.valueOf(member.getRole().name());

        return ProjectMemberResponse.builder()
                .userId(member.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(roleDto)
                .joinedAt(member.getJoinedAt())
                .build();
    }
}

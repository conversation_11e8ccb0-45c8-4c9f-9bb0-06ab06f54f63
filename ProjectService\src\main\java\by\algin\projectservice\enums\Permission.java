package by.algin.projectservice.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Permission {
    
    VIEW("View", "View project and its content"),
    EDIT("Edit", "Edit project settings and content"),
    DELETE("Delete", "Delete the project permanently"),
    MANAGE_MEMBERS("Manage Members", "Add, remove and change member roles"),
    INVITE("Invite", "Create and manage project invitations");
    
    private final String displayName;
    private final String description;
    

}

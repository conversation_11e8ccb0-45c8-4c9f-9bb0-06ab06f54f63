import java.util.*;

/**
 * Простой тест для проверки системы ролей
 * Этот файл демонстрирует как работает система ролей в проекте
 */
public class RoleSystemTest {
    
    public static void main(String[] args) {
        System.out.println("=== Анализ системы ролей проекта ===\n");
        
        // Анализ структуры ролей
        analyzeRoleStructure();
        
        // Анализ разрешений
        analyzePermissions();
        
        // Анализ проблем
        analyzeProblems();
        
        // Рекомендации по улучшению
        provideRecommendations();
    }
    
    private static void analyzeRoleStructure() {
        System.out.println("1. СТРУКТУРА РОЛЕЙ:");
        System.out.println("   OWNER (уровень 10):");
        System.out.println("   - Полный контроль над проектом");
        System.out.println("   - Может удалять проект");
        System.out.println("   - Максимум 1 на проект");
        System.out.println("   - Все разрешения");
        
        System.out.println("\n   MANAGER (уровень 7):");
        System.out.println("   - Управление участниками");
        System.out.println("   - Редактирование проекта");
        System.out.println("   - Не может удалять проект");
        System.out.println("   - Может назначать только DEVELOPER и VIEWER");
        
        System.out.println("\n   DEVELOPER (уровень 4):");
        System.out.println("   - Просмотр и редактирование проекта");
        System.out.println("   - Просмотр участников");
        System.out.println("   - Не может управлять участниками");
        
        System.out.println("\n   VIEWER (уровень 1):");
        System.out.println("   - Только просмотр проекта");
        System.out.println("   - Просмотр участников");
        System.out.println("   - Никаких изменений\n");
    }
    
    private static void analyzePermissions() {
        System.out.println("2. СИСТЕМА РАЗРЕШЕНИЙ:");
        
        Map<String, List<String>> rolePermissions = new HashMap<>();
        
        // OWNER permissions
        rolePermissions.put("OWNER", Arrays.asList(
            "PROJECT_VIEW", "PROJECT_EDIT", "PROJECT_DELETE", 
            "PROJECT_ARCHIVE", "PROJECT_STATUS_CHANGE",
            "MEMBER_VIEW", "MEMBER_ADD", "MEMBER_REMOVE", "MEMBER_ROLE_CHANGE",
            "INVITATION_CREATE", "INVITATION_CANCEL", "INVITATION_VIEW",
            "SETTINGS_VIEW", "SETTINGS_EDIT",
            "ROLE_ASSIGN_OWNER", "ROLE_ASSIGN_MANAGER", "ROLE_ASSIGN_DEVELOPER", "ROLE_ASSIGN_VIEWER",
            "AUDIT_VIEW"
        ));
        
        // MANAGER permissions
        rolePermissions.put("MANAGER", Arrays.asList(
            "PROJECT_VIEW", "PROJECT_EDIT", "PROJECT_ARCHIVE", "PROJECT_STATUS_CHANGE",
            "MEMBER_VIEW", "MEMBER_ADD", "MEMBER_REMOVE", "MEMBER_ROLE_CHANGE",
            "INVITATION_CREATE", "INVITATION_CANCEL", "INVITATION_VIEW",
            "SETTINGS_VIEW", "SETTINGS_EDIT",
            "ROLE_ASSIGN_DEVELOPER", "ROLE_ASSIGN_VIEWER",
            "METRICS_VIEW"
        ));
        
        // DEVELOPER permissions
        rolePermissions.put("DEVELOPER", Arrays.asList(
            "PROJECT_VIEW", "PROJECT_EDIT",
            "MEMBER_VIEW",
            "INVITATION_VIEW",
            "SETTINGS_VIEW"
        ));
        
        // VIEWER permissions
        rolePermissions.put("VIEWER", Arrays.asList(
            "PROJECT_VIEW",
            "MEMBER_VIEW",
            "SETTINGS_VIEW"
        ));
        
        for (Map.Entry<String, List<String>> entry : rolePermissions.entrySet()) {
            System.out.println("   " + entry.getKey() + " (" + entry.getValue().size() + " разрешений):");
            for (String permission : entry.getValue()) {
                System.out.println("     - " + permission);
            }
            System.out.println();
        }
    }
    
    private static void analyzeProblems() {
        System.out.println("3. ВЫЯВЛЕННЫЕ ПРОБЛЕМЫ:");
        
        System.out.println("   ❌ СЛОЖНОСТЬ:");
        System.out.println("     - Двойная система: конфигурация + база данных");
        System.out.println("     - Разрешения разбросаны по разным местам");
        System.out.println("     - Сложная логика проверки прав");
        
        System.out.println("\n   ❌ ПРОИЗВОДИТЕЛЬНОСТЬ:");
        System.out.println("     - Множественные запросы к БД для проверки прав");
        System.out.println("     - Нет кэширования разрешений");
        System.out.println("     - Проверка прав на каждый запрос");
        
        System.out.println("\n   ❌ ГИБКОСТЬ:");
        System.out.println("     - Жестко заданные роли в коде");
        System.out.println("     - Сложно добавить новые роли");
        System.out.println("     - Нет возможности кастомизации ролей");
        
        System.out.println("\n   ❌ БЕЗОПАСНОСТЬ:");
        System.out.println("     - Проверки разбросаны по сервисам");
        System.out.println("     - Возможны пропуски проверок");
        System.out.println("     - Нет централизованного аудита\n");
    }
    
    private static void provideRecommendations() {
        System.out.println("4. РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:");
        
        System.out.println("   ✅ УПРОЩЕНИЕ:");
        System.out.println("     - Использовать только конфигурацию (убрать БД)");
        System.out.println("     - Централизовать все разрешения в одном месте");
        System.out.println("     - Упростить логику проверки прав");
        
        System.out.println("\n   ✅ ПРОИЗВОДИТЕЛЬНОСТЬ:");
        System.out.println("     - Добавить кэширование разрешений");
        System.out.println("     - Загружать права один раз при входе");
        System.out.println("     - Использовать аннотации для проверки прав");
        
        System.out.println("\n   ✅ БЕЗОПАСНОСТЬ:");
        System.out.println("     - Создать единый SecurityService");
        System.out.println("     - Использовать аспекты для проверки прав");
        System.out.println("     - Добавить аудит всех операций");
        
        System.out.println("\n   ✅ ПРОСТОТА ИСПОЛЬЗОВАНИЯ:");
        System.out.println("     - @RequirePermission(\"PROJECT_EDIT\")");
        System.out.println("     - @RequireRole(\"MANAGER\")");
        System.out.println("     - Автоматическая проверка на уровне контроллера\n");
        
        System.out.println("5. ПРИМЕР УПРОЩЕННОЙ СИСТЕМЫ:");
        System.out.println("   enum Role { OWNER, MANAGER, DEVELOPER, VIEWER }");
        System.out.println("   enum Permission { VIEW, EDIT, DELETE, MANAGE_MEMBERS }");
        System.out.println("   Map<Role, Set<Permission>> rolePermissions");
        System.out.println("   @RequirePermission(Permission.EDIT)");
        System.out.println("   public void updateProject() { ... }\n");
    }
}

package by.algin.projectservice.util;

import by.algin.dto.response.UserResponse;
import by.algin.projectservice.exception.UserNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserBaseService {

    private final UserServiceClient userServiceClient;

    public UserResponse getUserByIdOrThrow(Long userId) {
        log.debug("Getting user by ID: {}", userId);
        try {
            return userServiceClient.getUserById(userId);
        } catch (Exception e) {
            log.error("Failed to get user by ID {}: {}", userId, e.getMessage());
            throw new UserNotFoundException("User not found with ID: " + userId);
        }
    }

    public List<UserResponse> getUsersByIds(List<Long> userIds) {
        log.debug("Getting users by IDs: {}", userIds);
        return userIds.stream()
                .map(this::getUserByIdOrThrow)
                .collect(Collectors.toList());
    }

    public Map<Long, UserResponse> getUsersByIdsAsMap(List<Long> userIds) {
        log.debug("Getting users by IDs as map: {}", userIds);
        return getUsersByIds(userIds).stream()
                .collect(Collectors.toMap(UserResponse::getId, user -> user));
    }

    public Map<Long, UserResponse> getUsersByIdsWithFallback(List<Long> userIds) {
        log.debug("Getting users by IDs with fallback: {}", userIds);
        return userIds.stream()
                .collect(Collectors.toMap(
                        id -> id,
                        this::getUserByIdWithFallback
                ));
    }

    private UserResponse getUserByIdWithFallback(Long userId) {
        try {
            return getUserByIdOrThrow(userId);
        } catch (UserNotFoundException e) {
            log.warn("User {} not found, creating fallback response", userId);
            return createFallbackUserResponse(userId);
        }
    }

    private UserResponse createFallbackUserResponse(Long userId) {
        UserResponse fallback = new UserResponse();
        fallback.setId(userId);
        fallback.setUsername("user_" + userId);
        fallback.setEmail("user_" + userId + "@unknown.com");
        return fallback;
    }

    public boolean userExists(Long userId) {
        try {
            getUserByIdOrThrow(userId);
            return true;
        } catch (UserNotFoundException e) {
            return false;
        }
    }
}

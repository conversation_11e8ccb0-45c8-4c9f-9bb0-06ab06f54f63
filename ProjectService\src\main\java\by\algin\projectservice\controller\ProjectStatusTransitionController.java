package by.algin.projectservice.controller;

import by.algin.dto.project.ProjectStatus;
import by.algin.dto.response.ApiResponse;
import by.algin.constants.CommonPathConstants;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.service.ProjectStatusTransitionService;

import by.algin.projectservice.util.ValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping(CommonPathConstants.API_STATUS_TRANSITIONS_BASE)
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "app.project.status-transitions.enabled", havingValue = "true", matchIfMissing = false)
public class ProjectStatusTransitionController {

    private final ProjectStatusTransitionService transitionService;
    private final ValidationUtils validationUtils;

    @GetMapping
    public ResponseEntity<Map<ProjectStatus, Set<ProjectStatus>>> getAllTransitions() {
        log.debug("Retrieving all status transitions");

        Map<ProjectStatus, Set<ProjectStatus>> transitions = transitionService.getAllTransitions();

        log.debug("Retrieved {} status transition groups", transitions.size());
        return ResponseEntity.ok(transitions);
    }

    @GetMapping(CommonPathConstants.STATUS_TRANSITIONS_BY_STATUS)
    public ResponseEntity<Set<ProjectStatus>> getTransitionsForStatus(@PathVariable ProjectStatus status) {
        log.debug("Getting valid transitions for status: {}", status);

        validationUtils.validateProjectStatus(status, "Status");

        Set<ProjectStatus> transitions = transitionService.getValidTransitions(status);

        log.debug("Found {} valid transitions for status {}", transitions.size(), status);
        return ResponseEntity.ok(transitions);
    }

    @GetMapping(CommonPathConstants.STATUS_TRANSITIONS_VALIDATE)
    public ResponseEntity<Boolean> validateTransition(@PathVariable ProjectStatus from,
                                                     @PathVariable ProjectStatus to,
                                                     @RequestParam(required = false) String userRole) {
        log.debug("Validating transition: {} -> {}, userRole: {}", from, to, userRole);

        validationUtils.validateProjectStatus(from, "From status");
        validationUtils.validateProjectStatus(to, "To status");
        validationUtils.validateUserRole(userRole);

        boolean isAllowed = transitionService.isTransitionAllowed(from, to, userRole);

        log.debug("Transition validation result: {} -> {} = {}", from, to, isAllowed);
        return ResponseEntity.ok(isAllowed);
    }

    @GetMapping(CommonPathConstants.STATUS_TRANSITIONS_DESCRIPTION)
    public ResponseEntity<String> getTransitionsDescription() {
        log.debug("Getting transitions description");

        String description = transitionService.getTransitionsDescription();

        log.debug("Retrieved transitions description with {} characters",
            description != null ? description.length() : 0);
        return ResponseEntity.ok(description);
    }

    @PostMapping(CommonPathConstants.STATUS_TRANSITIONS_VALIDATE_CONFIG)
    public ResponseEntity<ApiResponse<String>> validateConfiguration() {
        log.info("Starting status transitions configuration validation");

        try {
            transitionService.validateConfiguration();

            log.info("Status transitions configuration validation completed successfully");
            return ResponseEntity.ok(ApiResponse.success(
                ProjectMessageConstants.CONFIGURATION_VALIDATION_COMPLETED));

        } catch (Exception e) {
            log.error("Status transitions configuration validation failed: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error(
                "CONFIGURATION_VALIDATION_FAILED",
                ProjectMessageConstants.CONFIGURATION_VALIDATION_FAILED_WITH_MESSAGE + e.getMessage()));
        }
    }
}

package by.algin.projectservice.client;

import by.algin.constants.CommonServiceConstants;
import by.algin.projectservice.constants.ApiPathConstants;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
    name = CommonServiceConstants.USER_SERVICE,
    contextId = CommonServiceConstants.USER_SERVICE_CONTEXT_ID,
    path = ApiPathConstants.API_PATH,
    configuration = by.algin.projectservice.config.UserServiceFeignConfig.class
)
public interface UserServiceFeignClient {

    @GetMapping(ApiPathConstants.API_USERS_BY_ID)
    UserResponse getUserById(@PathVariable(ApiPathConstants.PARAM_USER_ID) Long userId);

    @GetMapping(ApiPathConstants.API_USERS)
    ApiResponse<List<UserResponse>> getUsersByIds(@RequestParam("ids") List<Long> userIds);

    @GetMapping(ApiPathConstants.API_USERS_SEARCH)
    ApiResponse<UserResponse> searchUsers(@RequestParam(ApiPathConstants.PARAM_FIELD) String field,
                                          @RequestParam(ApiPathConstants.PARAM_VALUE) String value);

    @GetMapping(ApiPathConstants.API_USERS_EXISTS)
    Boolean userExists(@PathVariable(ApiPathConstants.PARAM_USER_ID) Long userId);

    @GetMapping(ApiPathConstants.API_AUTH_VALIDATE)
    Boolean validateToken(@RequestHeader("Authorization") String authHeader);
}

package by.algin.projectservice.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Set;
import java.util.EnumSet;

@Getter
@RequiredArgsConstructor
public enum ProjectRole {
    
    OWNER(4, "Project Owner", "Full control over the project including deletion", 
          EnumSet.allOf(Permission.class), "#FF6B35", "crown", 1),
    
    MANAGER(3, "Project Manager", "Can manage project settings and members", 
            EnumSet.of(Permission.VIEW, Permission.EDIT, Permission.MANAGE_MEMBERS, Permission.INVITE), 
            "#4ECDC4", "gear", null),
    
    DEVELOPER(2, "Developer", "Can contribute to the project", 
              EnumSet.of(Permission.VIEW, Permission.EDIT), 
              "#45B7D1", "code", null),
    
    VIEWER(1, "Viewer", "Read-only access to the project", 
           EnumSet.of(Permission.VIEW), 
           "#95A5A6", "eye", null);
    
    private final int level;
    private final String displayName;
    private final String description;
    private final Set<Permission> permissions;
    private final String color;
    private final String icon;
    private final Integer maxPerProject;
    
    public boolean hasPermission(Permission permission) {
        return permissions.contains(permission);
    }

    public boolean canAssignRole(ProjectRole targetRole) {
        return this.level > targetRole.level;
    }

    public boolean isAdministrative() {
        return this == OWNER || this == MANAGER;
    }

    public boolean isMaxLimitReached(int currentCount) {
        return maxPerProject != null && currentCount >= maxPerProject;
    }
}

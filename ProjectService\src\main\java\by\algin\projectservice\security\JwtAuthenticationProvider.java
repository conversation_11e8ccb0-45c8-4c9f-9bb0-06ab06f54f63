package by.algin.projectservice.security;

import by.algin.projectservice.client.UserServiceFeignClient;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.exception.JwtAuthenticationException;
import by.algin.projectservice.exception.ProjectErrorCodes;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.util.Collections;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationProvider implements AuthenticationProvider {

    private final JwtUtil jwtUtil;
    private final UserServiceFeignClient userServiceFeignClient;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (!(authentication instanceof UsernamePasswordAuthenticationToken)) {
            throw new IllegalArgumentException("Unsupported authentication type: " + authentication.getClass());
        }

        String token = (String) authentication.getCredentials();
        log.debug("Authenticating JWT token");

        try {
            validateTokenLocally(token);
            validateTokenRemotely(token);
            String username = jwtUtil.extractUsername(token);
            Long userId = jwtUtil.extractUserId(token);

            if (username == null && userId == null) {
                throw new JwtAuthenticationException(
                    ProjectErrorCodes.JWT_TOKEN_INVALID,
                    "Token does not contain valid user information",
                    new RuntimeException("Invalid token content")
                );
            }

            log.debug(ProjectMessageConstants.EXTRACTED_USERNAME_AND_USERID, username, userId);

            return createAuthenticatedToken(username, userId);

        } catch (JwtAuthenticationException e) {
            log.warn("JWT authentication failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during JWT authentication: {}", e.getMessage(), e);
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_VALIDATION_FAILED,
                "JWT authentication failed due to unexpected error",
                e
            );
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private void validateTokenLocally(String token) {
        try {
            boolean isValid = jwtUtil.validateToken(token);
            if (!isValid) {
                if (jwtUtil.isTokenExpired(token)) {
                    throw new JwtAuthenticationException(
                        ProjectErrorCodes.JWT_TOKEN_EXPIRED,
                        "JWT token has expired",
                        new RuntimeException("Token expired")
                    );
                } else {
                    throw new JwtAuthenticationException(
                        ProjectErrorCodes.JWT_TOKEN_INVALID,
                        "JWT token is invalid",
                        new RuntimeException("Token invalid")
                    );
                }
            }
        } catch (JwtAuthenticationException e) {
            throw e;
        } catch (Exception e) {
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_TOKEN_INVALID,
                "JWT token validation failed",
                e
            );
        }
    }

    private void validateTokenRemotely(String token) {
        try {
            Boolean result = userServiceFeignClient.validateToken("Bearer " + token);
            if (result == null || !result) {
                throw new JwtAuthenticationException(
                    ProjectErrorCodes.JWT_VALIDATION_FAILED,
                    "Remote JWT token validation failed",
                    new RuntimeException("Remote validation failed")
                );
            }
        } catch (FeignException.Unauthorized e) {
            log.debug(ProjectMessageConstants.TOKEN_VALIDATION_FAILED_UNAUTHORIZED);
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_TOKEN_INVALID,
                "JWT token is unauthorized",
                e
            );
        } catch (FeignException e) {
            log.warn(ProjectMessageConstants.USER_SERVICE_TOKEN_VALIDATION_FAILED, e.getMessage());
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_VALIDATION_FAILED,
                "User service token validation failed",
                e
            );
        }
    }

    private UsernamePasswordAuthenticationToken createAuthenticatedToken(String username, Long userId) {
        String principal = userId != null ? userId.toString() : username;

        // Создаем токен с authorities - это автоматически делает его authenticated
        return new UsernamePasswordAuthenticationToken(
            principal,
            null,
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
        );
    }
}

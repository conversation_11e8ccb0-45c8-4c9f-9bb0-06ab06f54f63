*.md

bin/
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

.env
.env.local

.gitattributes
.mvn/wrapper/maven-wrapper.properties
mvnw
mvnw.cmd
src/main/resources/DB/sql.sql

*.log
logs/

*.tmp
*.temp
*~
*.bak
*.backup

src/main/resources/application.properties
pom.xml

*.md

**/test/**
*Test.java
*Tests.java
**/*Test.java
**/*Tests.java
src/test/
test/
package by.algin.projectservice.service;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.entity.ProjectStatusTransition;
import by.algin.projectservice.repository.ProjectStatusTransitionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "app.project.status-transitions.enabled", havingValue = "true", matchIfMissing = false)
public class ProjectStatusTransitionService {

    private final ProjectStatusTransitionRepository transitionRepository;
    private final AppProperties appProperties;

    @PostConstruct
    public void logTransitionConfiguration() {
        try {
            Map<ProjectStatus, Set<ProjectStatus>> allTransitions = getAllTransitions();
            allTransitions.forEach((from, toSet) -> {
                if (toSet.isEmpty()) {
                    log.info("Status {} -> No transitions allowed", from);
                } else {
                    log.info("Status {} -> {}", from, toSet);
                }
            });
        } catch (Exception e) {
            log.warn("Could not load transitions from database, using fallback configuration: {}", e.getMessage());
            logFallbackConfiguration();
        }
        log.info("=== End of Status Transitions Configuration ===");
    }

    public Set<ProjectStatus> getValidTransitions(ProjectStatus currentStatus) {
        try {
            List<ProjectStatusTransition> transitions = transitionRepository
                .findActiveTransitionsFromWithPrefix(currentStatus, LocalDateTime.now());

            return transitions.stream()
                .map(ProjectStatusTransition::getToStatus)
                .collect(Collectors.toSet());

        } catch (Exception e) {
            log.warn("Database unavailable for status transitions, using fallback configuration: {}", e.getMessage());
            return getFallbackTransitions(currentStatus);
        }
    }

    public boolean isTransitionAllowed(ProjectStatus from, ProjectStatus to) {
        return isTransitionAllowed(from, to, null);
    }

    public boolean isTransitionAllowed(ProjectStatus from, ProjectStatus to, String userRole) {
        try {
            return transitionRepository.findActiveTransitionWithPrefix(from, to, LocalDateTime.now())
                .map(transition -> userRole == null || transition.isAllowedForRole(userRole))
                .orElse(false);

        } catch (Exception e) {
            log.warn("Database unavailable for transition validation, using fallback: {}", e.getMessage());
            return getFallbackTransitions(from).contains(to);
        }
    }

    public Map<ProjectStatus, Set<ProjectStatus>> getAllTransitions() {
        try {
            List<ProjectStatusTransition> allTransitions = transitionRepository
                .findActiveTransitionsFrom(null, LocalDateTime.now());

            return allTransitions.stream()
                .filter(ProjectStatusTransition::isCurrentlyValid)
                .collect(Collectors.groupingBy(
                    ProjectStatusTransition::getFromStatus,
                    Collectors.mapping(
                        ProjectStatusTransition::getToStatus,
                        Collectors.toSet()
                    )
                ));

        } catch (Exception e) {
            log.error("Database error when getting all transitions, using fallback configuration: {}", e.getMessage(), e);
            return appProperties.getStatusTransitions().getAllTransitions();
        }
    }

    public void validateConfiguration() {
        Map<ProjectStatus, Set<ProjectStatus>> allTransitions = getAllTransitions();
        
        log.info("Validating status transitions configuration...");
        

        for (ProjectStatus status : ProjectStatus.values()) {
            if (!allTransitions.containsKey(status)) {
                log.warn("Status {} has no transition configuration", status);
            }
        }
        

        allTransitions.forEach((from, toSet) -> {
            toSet.forEach(to -> {
                if (to == null) {
                    log.error(ProjectMessageConstants.INVALID_TRANSITION_CONFIGURATION_NULL_TARGET, from);
                }
            });
        });
        
        log.info("Status transitions configuration validation completed");
    }

    public String getTransitionsDescription() {
        StringBuilder description = new StringBuilder();
        description.append("Project Status Transitions:\n");
        
        Map<ProjectStatus, Set<ProjectStatus>> allTransitions = getAllTransitions();
        allTransitions.forEach((from, toSet) -> {
            description.append("- ").append(from).append(" can transition to: ");
            if (toSet.isEmpty()) {
                description.append("(no transitions allowed)");
            } else {
                description.append(String.join(", ", toSet.stream().map(Enum::name).toArray(String[]::new)));
            }
            description.append("\n");
        });
        
        return description.toString();
    }

    @Transactional
    public ProjectStatusTransition addTransition(ProjectStatus from, ProjectStatus to,
                                               String description, String requiredRole, String createdBy) {
        ProjectStatusTransition transition = ProjectStatusTransition.builder()
            .fromStatus(from)
            .toStatus(to)
            .description(description)
            .requiredRole(requiredRole)
            .createdBy(createdBy)
            .isActive(true)
            .build();

        return transitionRepository.save(transition);
    }

    @Transactional
    public void removeTransition(ProjectStatus from, ProjectStatus to) {
        transitionRepository.findActiveTransitionWithPrefix(from, to, LocalDateTime.now())
            .ifPresent(transition -> {
                transition.setIsActive(false);
                transition.setUpdatedBy("SYSTEM");
                transitionRepository.save(transition);
            });
    }

    public List<ProjectStatusTransition> getAllTransitionsForAdmin() {
        return transitionRepository.findAllByOrderByFromStatusAscToStatusAsc();
    }

    @Transactional
    public void toggleTransition(Long transitionId, boolean isActive, String updatedBy) {
        transitionRepository.findById(transitionId)
            .ifPresent(transition -> {
                transition.setIsActive(isActive);
                transition.setUpdatedBy(updatedBy);
                transitionRepository.save(transition);
            });
    }

    private void logFallbackConfiguration() {
        Map<ProjectStatus, Set<ProjectStatus>> fallbackTransitions =
            appProperties.getStatusTransitions().getAllTransitions();

        fallbackTransitions.forEach((from, toSet) -> {
            if (toSet.isEmpty()) {
                log.info("Fallback: Status {} -> No transitions allowed", from);
            } else {
                log.info("Fallback: Status {} -> {}", from, toSet);
            }
        });
    }

    private Set<ProjectStatus> getFallbackTransitions(ProjectStatus currentStatus) {
        return appProperties.getStatusTransitions().getTransitionsFor(currentStatus);
    }
}

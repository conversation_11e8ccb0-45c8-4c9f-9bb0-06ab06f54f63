package by.algin.projectservice.constants;

/**
 * Local API path constants for ProjectService
 * Independent from CommonDTOs to avoid compilation issues
 */
public final class ApiPathConstants {

    // User API paths
    public static final String API_PATH = "/api";
    public static final String API_USERS = "/api/users";
    public static final String API_USERS_BY_ID = "/users/{userId}";
    public static final String API_USERS_SEARCH = "/users/search";
    public static final String API_USERS_EXISTS = "/users/{userId}/exists";
    
    // Auth API paths
    public static final String API_AUTH_VALIDATE = "/auth/validate";
    
    // Project API paths
    public static final String API_PROJECTS = "/projects";
    public static final String API_PROJECTS_BY_ID = "/projects/{projectId}";
    public static final String API_PROJECTS_MEMBERS = "/projects/{projectId}/members";
    public static final String API_PROJECTS_MEMBERS_ROLE = "/projects/{projectId}/members/{userId}/role";
    public static final String API_PROJECTS_MEMBERS_DELETE = "/projects/{projectId}/members/{userId}";
    
    // Path variables
    public static final String PATH_VAR_PROJECT_ID = "/{projectId}";
    public static final String PATH_VAR_USER_ID = "/{userId}";
    public static final String PATH_VAR_PROJECT_ID_MEMBERS = "/{projectId}/members";
    public static final String PATH_VAR_PROJECT_ID_USER_ID = "/{projectId}/members/{userId}";
    public static final String PATH_VAR_PROJECT_ID_USER_ID_ROLE = "/{projectId}/members/{userId}/role";
    
    // Request parameters
    public static final String PARAM_FIELD = "field";
    public static final String PARAM_VALUE = "value";
    public static final String PARAM_USER_ID = "userId";
    public static final String PARAM_PROJECT_ID = "projectId";
    
    // Common endpoints
    public static final String TEST_ENDPOINT = "/test";
    public static final String ROLES_ENDPOINT = "/roles";
    
    private ApiPathConstants() {
        // Utility class
    }
}

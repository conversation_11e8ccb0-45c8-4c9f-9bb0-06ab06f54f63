package by.algin.projectservice.controller;

import by.algin.api.ProjectRoleApi;
import by.algin.dto.project.ProjectRoleResponse;
import by.algin.dto.response.ApiResponse;
import by.algin.projectservice.enums.ProjectRole;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ProjectRoleController implements ProjectRoleApi {

    @Override
    public ApiResponse<List<ProjectRoleResponse>> getAvailableRoles() {
        log.debug("Getting all available project roles");

        List<ProjectRoleResponse> roleResponses = Arrays.stream(by.algin.projectservice.enums.ProjectRole.values())
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());

        return ApiResponse.success(roleResponses);
    }

    @Override
    public ApiResponse<List<ProjectRoleResponse>> getAssignableRoles() {
        log.debug("Getting assignable project roles");

        List<ProjectRoleResponse> roleResponses = Arrays.stream(by.algin.projectservice.enums.ProjectRole.values())
                .filter(role -> role != by.algin.projectservice.enums.ProjectRole.OWNER)
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());

        return ApiResponse.success(roleResponses);
    }
    
    private ProjectRoleResponse convertToRoleResponse(by.algin.projectservice.enums.ProjectRole role) {
        return ProjectRoleResponse.builder()
                .roleName(role.name())
                .displayName(role.getDisplayName())
                .description(role.getDescription())
                .permissionLevel(role.getLevel())
                .colorCode(role.getColor())
                .iconName(role.getIcon())
                .isActive(true)
                .canBeAssigned(role != by.algin.projectservice.enums.ProjectRole.OWNER)
                .maxPerProject(role == by.algin.projectservice.enums.ProjectRole.OWNER ? 1 : null)
                .isSystemRole(true)
                .build();
    }
}

-- Recreate schema after manual deletion
-- Drop tables if they exist (cleanup)
DROP TABLE IF EXISTS project_invitations CASCADE;
DROP TABLE IF EXISTS project_members CASCADE;
DROP TABLE IF EXISTS projects CASCADE;

-- Drop functions if they exist
DROP FUNCTION IF EXISTS check_role_hierarchy(VA<PERSON>HA<PERSON>, VARCHAR);
DROP FUNCTION IF EXISTS has_permission(VARC<PERSON>R, VARCHAR);
DROP FUNCTION IF EXISTS check_owner_limit();
DROP FUNCTION IF EXISTS update_project_timestamp();

-- Recreate all tables and functions
CREATE TABLE IF NOT EXISTS projects (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    owner_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_project_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'COMPLETED', 'ARCHIVED')),
    CONSTRAINT chk_project_name_length CHECK (LENGTH(name) >= 2)
);

CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name);
CREATE UNIQUE INDEX IF NOT EXISTS idx_projects_name_unique ON projects(LOWER(name));

-- Simplified project members table with enum roles
CREATE TABLE IF NOT EXISTS project_members (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('OWNER', 'MANAGER', 'DEVELOPER', 'VIEWER')),
    joined_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_project_members_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT uk_project_members_unique UNIQUE (project_id, user_id)
);

-- Indexes for project_members
CREATE INDEX IF NOT EXISTS idx_project_members_project_id ON project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user_id ON project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_role ON project_members(role);
CREATE INDEX IF NOT EXISTS idx_project_members_joined_at ON project_members(joined_at DESC);
CREATE INDEX IF NOT EXISTS idx_project_members_project_role ON project_members(project_id, role);
CREATE INDEX IF NOT EXISTS idx_project_members_user_joined ON project_members(user_id, joined_at DESC);

-- Project invitations with enum roles
CREATE TABLE IF NOT EXISTS project_invitations (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    invited_email VARCHAR(255),
    accepted_email VARCHAR(255),
    role VARCHAR(20) NOT NULL CHECK (role IN ('MANAGER', 'DEVELOPER', 'VIEWER')),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'CANCELLED', 'EXPIRED')),
    token VARCHAR(255) NOT NULL UNIQUE,
    invited_by_user_id BIGINT NOT NULL,
    accepted_by_user_id BIGINT,
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP,

    CONSTRAINT fk_project_invitations_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Indexes for project_invitations
CREATE INDEX IF NOT EXISTS idx_project_invitations_project_id ON project_invitations(project_id);
CREATE INDEX IF NOT EXISTS idx_project_invitations_email ON project_invitations(invited_email);
CREATE INDEX IF NOT EXISTS idx_project_invitations_status ON project_invitations(status);
CREATE INDEX IF NOT EXISTS idx_project_invitations_token ON project_invitations(token);
CREATE INDEX IF NOT EXISTS idx_project_invitations_expires_at ON project_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_project_invitations_invited_by ON project_invitations(invited_by_user_id);
CREATE INDEX IF NOT EXISTS idx_project_invitations_accepted_by ON project_invitations(accepted_by_user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_project_invitations_unique_pending
    ON project_invitations(project_id, invited_email)
    WHERE status = 'PENDING';

-- Business logic functions for role management
-- Function to check role hierarchy for assignments
CREATE OR REPLACE FUNCTION check_role_hierarchy(user_role VARCHAR(20), target_role VARCHAR(20)) 
RETURNS BOOLEAN AS $$
BEGIN
    IF user_role = 'OWNER' THEN
        RETURN TRUE;
    END IF;
    
    IF user_role = 'MANAGER' AND target_role IN ('DEVELOPER', 'VIEWER') THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to check permissions
CREATE OR REPLACE FUNCTION has_permission(user_role VARCHAR(20), permission VARCHAR(50)) 
RETURNS BOOLEAN AS $$
BEGIN
    CASE user_role
        WHEN 'OWNER' THEN
            RETURN TRUE;
        WHEN 'MANAGER' THEN
            RETURN permission IN ('VIEW', 'EDIT', 'MANAGE_MEMBERS', 'INVITE');
        WHEN 'DEVELOPER' THEN
            RETURN permission IN ('VIEW', 'EDIT');
        WHEN 'VIEWER' THEN
            RETURN permission = 'VIEW';
        ELSE
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Trigger to enforce owner limit (only one owner per project)
CREATE OR REPLACE FUNCTION check_owner_limit() 
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.role = 'OWNER' THEN
        IF (SELECT COUNT(*) FROM project_members 
            WHERE project_id = NEW.project_id AND role = 'OWNER' AND id != COALESCE(NEW.id, 0)) >= 1 THEN
            RAISE EXCEPTION 'Project can have only one owner';
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_owner_limit
    BEFORE INSERT OR UPDATE ON project_members
    FOR EACH ROW EXECUTE FUNCTION check_owner_limit();

-- Trigger to update project updated_at timestamp
CREATE OR REPLACE FUNCTION update_project_timestamp() 
RETURNS TRIGGER AS $$
BEGIN
    UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.project_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_project_timestamp
    AFTER INSERT OR UPDATE OR DELETE ON project_members
    FOR EACH ROW EXECUTE FUNCTION update_project_timestamp();
